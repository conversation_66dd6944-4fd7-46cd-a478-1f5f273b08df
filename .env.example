# TradingView Configuration
TRADINGVIEW_UPDATE_INTERVAL=1000
TRADINGVIEW_SYMBOL=EURUSD
TRADINGVIEW_EXCHANGE=FX_IDC

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=test
TELEGRAM_CHAT_ID=test

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/forex-trading-bot
MONGODB_DB_NAME=forex-trading-bot

# Server Configuration
PORT=3003
NODE_ENV=development

# Trading Configuration
DEFAULT_SYMBOL=EUR/USD
DEFAULT_INTERVAL=10s
MAX_SIGNALS_PER_DAY=50
MIN_CONFIDENCE_LEVEL=30
DEFAULT_RISK_REWARD_RATIO=1.5
MAX_RISK_PER_TRADE=2.0
STOP_LOSS_PIPS=20
TAKE_PROFIT_PIPS=30

# Technical Indicators Configuration
RSI_PERIOD=14
EMA_PERIOD=20
MACD_FAST=12
MACD_SLOW=26
MACD_SIGNAL=9
BOLLINGER_PERIOD=20
BOLLINGER_STDDEV=2

# Risk Management
DEFAULT_RISK_REWARD_RATIO=1.5
MAX_RISK_PER_TRADE=2
STOP_LOSS_PIPS=20
TAKE_PROFIT_PIPS=30

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/trading-bot.log

# WebSocket Configuration
WS_RECONNECT_INTERVAL=5000
WS_MAX_RECONNECT_ATTEMPTS=10
WS_PING_INTERVAL=30000
