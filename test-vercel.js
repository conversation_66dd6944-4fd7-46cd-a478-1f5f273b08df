// Simple test to verify the Vercel function works
const app = require('./dist/vercel.js').default;

// Test the health endpoint
console.log('Testing Vercel function...');

// Mock request and response objects
const mockReq = {
  method: 'GET',
  url: '/health',
  headers: {},
  body: {}
};

const mockRes = {
  status: function(code) {
    this.statusCode = code;
    return this;
  },
  json: function(data) {
    console.log(`Response Status: ${this.statusCode}`);
    console.log('Response Data:', JSON.stringify(data, null, 2));
    return this;
  },
  statusCode: 200
};

// Test if the app is properly exported
if (app && typeof app === 'function') {
  console.log('✅ Vercel function exported successfully');
  console.log('✅ Express app is ready for deployment');
} else {
  console.log('❌ Vercel function export failed');
  process.exit(1);
}

console.log('🚀 Vercel function test completed successfully!');
