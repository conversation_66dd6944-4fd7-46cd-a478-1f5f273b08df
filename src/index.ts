import cors from 'cors';
import express from 'express';
import helmet from 'helmet';
import morgan from 'morgan';
import { config, validateConfig } from './config';
import { logger } from './utils/logger';

class TradingBot {
  private app: express.Application;
  private server: any;
  private servicesInitialized: boolean = false;
  private initializationPromise: Promise<void> | null = null;

  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  // Get the Express app for Vercel deployment
  public getApp(): express.Application {
    return this.app;
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet());

    // CORS is not needed for this trading bot
    const url =
      process.env.NODE_ENV === 'production'
        ? process.env.APP_URL
        : 'http://localhost:3000';
    this.app.use(
      cors({
        origin: url
      })
    );

    // Logging middleware
    this.app.use(
      morgan('combined', {
        stream: {
          write: (message: string) => {
            logger.info('HTTP', message.trim());
          }
        }
      })
    );

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  }

  // Lazy initialization for serverless environment
  private async ensureServicesInitialized(): Promise<void> {
    if (this.servicesInitialized) {
      return;
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this.initializeServices();
    await this.initializationPromise;
    this.servicesInitialized = true;
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', async (_req, res) => {
      try {
        await this.ensureServicesInitialized();
        res.json({
          status: 'ok',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          environment: config.nodeEnv,
          services: {
            telegram: 'initialized',
            tradingView: 'initialized',
            signals: 'initialized'
          }
        });
      } catch (error) {
        logger.error('HEALTH', 'Health check failed', { error });
        res.status(503).json({
          status: 'error',
          timestamp: new Date().toISOString(),
          error: 'Service initialization failed'
        });
      }
    });

    // API status endpoint
    this.app.get('/api/status', async (_req, res) => {
      try {
        await this.ensureServicesInitialized();
        res.json({
          success: true,
          data: {
            service: 'AI Trading Bot',
            version: '1.0.0',
            status: 'running',
            timestamp: new Date().toISOString(),
            environment: config.nodeEnv
          }
        });
      } catch (error) {
        logger.error('API', 'Status check failed', { error });
        res.status(503).json({
          success: false,
          error: 'Service initialization failed',
          timestamp: new Date().toISOString()
        });
      }
    });

    // Telegram webhook endpoint
    this.app.post('/webhook', async (req, res) => {
      try {
        await this.ensureServicesInitialized();
        const { telegramService } = await import('./services/telegram');
        if (telegramService && telegramService.isInitialized) {
          logger.info('WEBHOOK', 'Processing Telegram webhook update', {
            updateId: req.body.update_id,
            hasMessage: !!req.body.message,
            hasCallbackQuery: !!req.body.callback_query
          });
          telegramService.handleWebhookUpdate(req.body);
        } else {
          logger.warn(
            'WEBHOOK',
            'Telegram service not initialized, ignoring webhook'
          );
        }
        res.status(200).send('OK');
      } catch (error) {
        logger.error('WEBHOOK', 'Error processing webhook', { error });
        res.status(500).send('Error');
      }
    });

    // Webhook setup endpoint for manual webhook configuration
    this.app.post('/setup-webhook', async (_req, res) => {
      try {
        await this.ensureServicesInitialized();
        const { telegramService } = await import('./services/telegram');

        if (telegramService && telegramService.isInitialized) {
          // Force webhook setup
          await telegramService.setupWebhook();
          res.json({
            success: true,
            message: 'Webhook setup completed',
            timestamp: new Date().toISOString()
          });
        } else {
          res.status(503).json({
            success: false,
            error: 'Telegram service not initialized',
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        logger.error('WEBHOOK_SETUP', 'Error setting up webhook', { error });
        res.status(500).json({
          success: false,
          error: 'Failed to setup webhook',
          details: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        });
      }
    });

    // Webhook status endpoint to check webhook configuration
    this.app.get('/webhook-status', async (_req, res) => {
      try {
        await this.ensureServicesInitialized();
        const { telegramService } = await import('./services/telegram');

        if (telegramService && telegramService.isInitialized) {
          // Get webhook info from Telegram
          const webhookInfo = await (
            telegramService as any
          ).bot.getWebHookInfo();
          res.json({
            success: true,
            webhook: {
              url: webhookInfo.url,
              hasCustomCertificate: webhookInfo.has_custom_certificate,
              pendingUpdateCount: webhookInfo.pending_update_count,
              lastErrorDate: webhookInfo.last_error_date,
              lastErrorMessage: webhookInfo.last_error_message,
              maxConnections: webhookInfo.max_connections,
              allowedUpdates: webhookInfo.allowed_updates
            },
            environment: {
              nodeEnv: process.env.NODE_ENV,
              vercel: !!process.env.VERCEL,
              appUrl: process.env.APP_URL,
              vercelUrl: process.env.VERCEL_URL
            },
            timestamp: new Date().toISOString()
          });
        } else {
          res.status(503).json({
            success: false,
            error: 'Telegram service not initialized',
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        logger.error('WEBHOOK_STATUS', 'Error getting webhook status', {
          error
        });
        res.status(500).json({
          success: false,
          error: 'Failed to get webhook status',
          details: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        });
      }
    });

    // 404 handler
    this.app.use('*', (_req, res) => {
      res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        timestamp: new Date().toISOString()
      });
    });

    // Error handler
    this.app.use(
      (
        error: any,
        _req: express.Request,
        res: express.Response,
        _next: express.NextFunction
      ) => {
        logger.error('EXPRESS', 'Unhandled error', {
          error: error.message,
          stack: error.stack
        });
        res.status(500).json({
          success: false,
          error: 'Internal server error',
          timestamp: new Date().toISOString()
        });
      }
    );
  }

  public async start(): Promise<void> {
    try {
      // Validate configuration
      validateConfig();
      logger.info('APP', 'Configuration validated successfully');

      // Initialize services
      await this.initializeServices();

      // Start HTTP server (only in non-serverless environment)
      if (!process.env.VERCEL && !process.env.AWS_LAMBDA_FUNCTION_NAME) {
        this.server = this.app.listen(config.port, () => {
          logger.info(
            'APP',
            `🚀 Trading Bot server started on port ${config.port}`
          );
          logger.info('APP', `📊 Environment: ${config.nodeEnv}`);
          logger.info('APP', `💱 Default symbol: ${config.trading.symbol}`);
          logger.info('APP', `⏱️  Interval: ${config.trading.interval}`);
        });

        // Graceful shutdown handlers (only for traditional server)
        this.setupGracefulShutdown();
      } else {
        logger.info(
          'APP',
          '🚀 Trading Bot initialized for serverless deployment'
        );
        logger.info('APP', `📊 Environment: ${config.nodeEnv}`);
        logger.info('APP', `💱 Default symbol: ${config.trading.symbol}`);
        logger.info('APP', `⏱️  Interval: ${config.trading.interval}`);
      }
    } catch (error) {
      logger.error('APP', 'Failed to start trading bot', { error });
      if (!process.env.VERCEL && !process.env.AWS_LAMBDA_FUNCTION_NAME) {
        process.exit(1);
      } else {
        throw error; // Let serverless environment handle the error
      }
    }
  }

  private async initializeServices(): Promise<void> {
    logger.info('APP', 'Initializing services...');

    try {
      // Initialize database connection
      // await DatabaseService.connect();
      logger.info('APP', '✅ Database service initialized');

      // Initialize Telegram bot
      const { telegramService } = await import('./services/telegram');
      await telegramService.initialize();
      logger.info('APP', '✅ Telegram service initialized');

      // Initialize TradingView market data manager
      const { tradingViewMarketDataManager } = await import(
        './services/tradingview/market-data-manager'
      );
      await tradingViewMarketDataManager.start();
      logger.info('APP', '✅ TradingView service initialized');

      // Initialize signal manager
      const { signalManager } = await import('./services/signals/manager');
      signalManager.setEnabled(true);
      logger.info('APP', '✅ Signal manager initialized');

      logger.info('APP', '🎯 All services initialized successfully');
    } catch (error) {
      logger.error('APP', 'Failed to initialize services', { error });
      throw error;
    }
  }

  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      logger.info('APP', `Received ${signal}, starting graceful shutdown...`);

      try {
        // Close HTTP server
        if (this.server) {
          this.server.close(() => {
            logger.info('APP', 'HTTP server closed');
          });
        }

        // Close services
        const { tradingViewMarketDataManager } = await import(
          './services/tradingview/market-data-manager'
        );
        await tradingViewMarketDataManager.stop();
        const { signalManager } = await import('./services/signals/manager');
        signalManager.setEnabled(false);
        const { telegramService } = await import('./services/telegram');
        await telegramService.stop();
        // await DatabaseService.disconnect();

        logger.info('APP', 'Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('APP', 'Error during shutdown', { error });
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // nodemon restart

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('APP', 'Uncaught exception', {
        error: error.message,
        stack: error.stack
      });
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('APP', 'Unhandled rejection', { reason, promise });
      process.exit(1);
    });
  }
}

// Create the trading bot instance
const bot = new TradingBot();

// Determine the export based on environment
let defaultExport: any;

if (process.env.VERCEL || process.env.AWS_LAMBDA_FUNCTION_NAME) {
  // Serverless environment - export the Express app
  logger.info('APP', 'Preparing Express app for serverless deployment');

  // Validate configuration on module load
  try {
    validateConfig();
    logger.info('APP', 'Configuration validated for serverless deployment');
  } catch (error) {
    logger.error('APP', 'Configuration validation failed', { error });
  }

  // Export the Express app for Vercel
  defaultExport = bot.getApp();
} else {
  // Traditional server environment - start the application
  bot.start().catch((error) => {
    console.error('Failed to start trading bot:', error);
    process.exit(1);
  });

  // Export the TradingBot class for traditional deployment
  defaultExport = TradingBot;
}

export default defaultExport;
