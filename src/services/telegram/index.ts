import { EventEmitter } from 'events';
import TelegramBot from 'node-telegram-bot-api';
import { config } from '../../config';
import { AdvancedSignal, TelegramMessage } from '../../types';
import { logger } from '../../utils/logger';

export interface TelegramConfig {
  botToken: string;
  chatId: string;
  enabled: boolean;
  messageFormat: 'simple' | 'detailed' | 'advanced';
  includeCharts: boolean;
  enableCommands: boolean;
}

export interface SignalMessageData {
  signal: AdvancedSignal;
  positionSize?: number;
  timestamp: number;
}

export class TelegramService extends EventEmitter {
  private bot: TelegramBot | null = null;
  private telegramConfig: TelegramConfig;
  public isInitialized = false;
  private messageQueue: TelegramMessage[] = [];
  private isProcessingQueue = false;

  constructor() {
    super();
    this.telegramConfig = this.initializeConfig();
  }

  private initializeConfig(): TelegramConfig {
    return {
      botToken: config.telegram.botToken,
      chatId: config.telegram.chatId,
      enabled: true,
      messageFormat: 'detailed',
      includeCharts: false,
      enableCommands: true
    };
  }

  /**
   * Initialize Telegram bot
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.telegram('warn', 'Telegram service already initialized');
      return;
    }

    try {
      if (
        !this.telegramConfig.botToken ||
        this.telegramConfig.botToken === 'demo_bot_token_for_testing'
      ) {
        logger.telegram(
          'warn',
          'Demo Telegram bot token detected, skipping initialization'
        );
        this.isInitialized = true;
        return;
      }

      // Initialize bot
      // Use webhooks in production, polling in development
      const isProduction = process.env.NODE_ENV === 'production';

      if (isProduction) {
        this.bot = new TelegramBot(this.telegramConfig.botToken, {
          polling: false
        });
      } else {
        this.bot = new TelegramBot(this.telegramConfig.botToken, {
          polling: {
            interval: 100, // Check for messages every 100ms for instant responses
            autoStart: true,
            params: {
              timeout: 10 // Timeout for long polling
            }
          }
        });
      }

      // Test bot connection
      const botInfo = await this.bot.getMe();
      logger.telegram(
        'info',
        `Telegram bot initialized: @${botInfo.username}`,
        {
          id: botInfo.id,
          firstName: botInfo.first_name
        }
      );

      // Set up commands if enabled
      if (this.telegramConfig.enableCommands) {
        await this.setupCommands();
      }

      // Set up webhook in production
      if (process.env.NODE_ENV === 'production' && process.env.VERCEL_URL) {
        await this.setupWebhook();
      }

      // Send startup message
      await this.sendStartupMessage();

      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      logger.telegram('error', 'Failed to initialize Telegram bot', { error });
      throw error;
    }
  }

  /**
   * Set up webhook for production deployment
   */
  private async setupWebhook(): Promise<void> {
    try {
      const webhookUrl = `https://${process.env.VERCEL_URL}/webhook`;

      // Delete existing webhook first
      await this.bot!.deleteWebHook();

      // Set new webhook
      await this.bot!.setWebHook(webhookUrl, {
        allowed_updates: ['message', 'callback_query']
      });

      logger.telegram('info', 'Webhook set successfully', { webhookUrl });
    } catch (error) {
      logger.telegram('error', 'Failed to set webhook', { error });
      throw error;
    }
  }

  /**
   * Handle webhook updates
   */
  public handleWebhookUpdate(update: any): void {
    try {
      if (update.message) {
        // Process the message through the bot's internal handler
        this.bot!.processUpdate(update);
      }
    } catch (error) {
      logger.telegram('error', 'Error processing webhook update', { error });
    }
  }

  /**
   * Set up bot commands
   */
  private async setupCommands(): Promise<void> {
    if (!this.bot) return;

    try {
      const commands = [
        {
          command: 'start',
          description: 'Start the bot and get welcome message'
        },
        {
          command: 'status',
          description: 'Get current bot status and statistics'
        },
        { command: 'signals', description: 'Get recent trading signals' },
        { command: 'config', description: 'View current configuration' },
        { command: 'help', description: 'Show available commands' }
      ];

      await this.bot.setMyCommands(commands);
      logger.telegram('info', 'Bot commands set up successfully');

      // Set up command handlers with immediate response and timeout
      this.bot.onText(/\/start/, (msg) => {
        // Handle command asynchronously with timeout for instant response
        Promise.race([
          this.handleStartCommand(msg),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Command timeout')), 5000)
          )
        ]).catch((error) =>
          logger.telegram('error', 'Error in /start handler', {
            error,
            chatId: msg.chat.id
          })
        );
      });

      this.bot.onText(/\/status/, (msg) => {
        Promise.race([
          this.handleStatusCommand(msg),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Command timeout')), 5000)
          )
        ]).catch((error) =>
          logger.telegram('error', 'Error in /status handler', {
            error,
            chatId: msg.chat.id
          })
        );
      });

      this.bot.onText(/\/signals/, (msg) => {
        Promise.race([
          this.handleSignalsCommand(msg),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Command timeout')), 5000)
          )
        ]).catch((error) =>
          logger.telegram('error', 'Error in /signals handler', {
            error,
            chatId: msg.chat.id
          })
        );
      });

      this.bot.onText(/\/config/, (msg) => {
        Promise.race([
          this.handleConfigCommand(msg),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Command timeout')), 5000)
          )
        ]).catch((error) =>
          logger.telegram('error', 'Error in /config handler', {
            error,
            chatId: msg.chat.id
          })
        );
      });

      this.bot.onText(/\/help/, (msg) => {
        Promise.race([
          this.handleHelpCommand(msg),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Command timeout')), 5000)
          )
        ]).catch((error) =>
          logger.telegram('error', 'Error in /help handler', {
            error,
            chatId: msg.chat.id
          })
        );
      });
    } catch (error) {
      logger.telegram('error', 'Failed to set up bot commands', { error });
    }
  }

  /**
   * Send startup message
   */
  private async sendStartupMessage(): Promise<void> {
    logger.telegram('info', 'Sending startup message', {
      chatId: this.telegramConfig.chatId,
      enabled: this.telegramConfig.enabled
    });

    try {
      const message =
        `🤖 *AI Trading Bot Started*\n\n` +
        `✅ Bot is now online and ready to send trading signals\n` +
        `📊 Monitoring: ${config.trading.symbol}\n` +
        `⏰ Started: ${new Date().toLocaleString()}\n\n` +
        `Use /help to see available commands`;

      await this.sendMessage({
        chatId: this.telegramConfig.chatId,
        text: message,
        parseMode: 'Markdown'
      });

      logger.telegram('info', 'Startup message sent successfully');
    } catch (error) {
      logger.telegram('error', 'Failed to send startup message', { error });
    }
  }

  /**
   * Send trading signal message
   */
  public async sendSignal(data: SignalMessageData): Promise<void> {
    logger.telegram('info', 'sendSignal called', {
      enabled: this.telegramConfig.enabled,
      initialized: this.isInitialized,
      chatId: this.telegramConfig.chatId,
      symbol: data.signal.symbol
    });

    if (!this.telegramConfig.enabled || !this.isInitialized) {
      logger.telegram(
        'warn',
        'Telegram service disabled or not initialized, skipping signal'
      );
      return;
    }

    try {
      const message = this.formatSignalMessage(data);
      logger.telegram('info', 'Formatted signal message', {
        messageLength: message.length
      });

      await this.sendMessage({
        chatId: this.telegramConfig.chatId,
        text: message,
        parseMode: 'HTML'
      });

      logger.telegram('info', `Signal sent for ${data.signal.symbol}`, {
        type: data.signal.type,
        confidence: data.signal.confidence
      });

      this.emit('signalSent', data);
    } catch (error) {
      logger.telegram(
        'error',
        `Failed to send signal for ${data.signal.symbol}`,
        { error }
      );
    }
  }

  /**
   * Format signal message based on configuration
   */
  private formatSignalMessage(data: SignalMessageData): string {
    const { signal, positionSize } = data;

    switch (this.telegramConfig.messageFormat) {
      case 'simple':
        return this.formatSimpleMessage(signal, positionSize);
      case 'detailed':
        return this.formatDetailedMessage(signal, positionSize);
      case 'advanced':
        return this.formatAdvancedMessage(signal, positionSize);
      default:
        return this.formatDetailedMessage(signal, positionSize);
    }
  }

  /**
   * Format simple signal message
   */
  private formatSimpleMessage(
    signal: AdvancedSignal,
    positionSize?: number
  ): string {
    const direction = signal.type === 'BUY' ? '📈' : '📉';
    const confidence = Math.round(signal.confidence);

    return (
      `🔔 <b>FOREX SIGNAL</b>\n\n` +
      `${direction} <b>${signal.type} ${signal.symbol}</b>\n` +
      `💰 Entry: ${signal.entryPrice.toFixed(5)}\n` +
      `🛑 Stop Loss: ${signal.stopLoss.toFixed(5)}\n` +
      `🎯 Take Profit: ${signal.takeProfit.toFixed(5)}\n` +
      `📊 Confidence: ${confidence}%\n` +
      `⏰ ${new Date(signal.timestamp).toLocaleString()}`
    );
  }

  /**
   * Format detailed signal message
   */
  private formatDetailedMessage(
    signal: AdvancedSignal,
    positionSize?: number
  ): string {
    const direction = signal.type === 'BUY' ? '📈' : '📉';
    const confidence = Math.round(signal.confidence);
    const riskReward = signal.riskRewardRatio.toFixed(2);

    // Calculate pips
    const stopLossPips = Math.abs(signal.entryPrice - signal.stopLoss) * 10000;
    const takeProfitPips =
      Math.abs(signal.takeProfit - signal.entryPrice) * 10000;

    let message =
      `🔔 <b>FOREX SIGNAL</b>\n\n` +
      `${direction} <b>${signal.type} ${signal.symbol}</b>\n` +
      `⏰ Time: ${new Date(signal.timestamp).toLocaleString()} UTC\n\n` +
      `💰 Entry: <b>${signal.entryPrice.toFixed(5)}</b>\n` +
      `🛑 Stop Loss: ${signal.stopLoss.toFixed(5)} (-${stopLossPips.toFixed(
        0
      )} pips)\n` +
      `🎯 Take Profit: ${signal.takeProfit.toFixed(
        5
      )} (+${takeProfitPips.toFixed(0)} pips)\n` +
      `📊 Risk/Reward: 1:${riskReward}\n` +
      `🎯 Confidence: <b>${confidence}%</b>\n\n`;

    // Add indicators
    if (signal.indicators) {
      message += `📈 <b>Indicators:</b>\n`;
      message += `• RSI: ${signal.indicators.rsi.toFixed(1)}\n`;
      message += `• EMA: ${signal.indicators.ema.toFixed(5)}\n`;
      message += `• MACD: ${signal.indicators.macd.macd.toFixed(4)}\n`;
    }

    // Add patterns
    if (signal.patterns && signal.patterns.length > 0) {
      const strongPatterns = signal.patterns.filter((p) => p.strength > 70);
      if (strongPatterns.length > 0) {
        message += `\n🕯️ <b>Patterns:</b>\n`;
        strongPatterns.forEach((pattern) => {
          const emoji =
            pattern.type === 'bullish'
              ? '🟢'
              : pattern.type === 'bearish'
              ? '🔴'
              : '🟡';
          message += `${emoji} ${pattern.name} (${pattern.strength}%)\n`;
        });
      }
    }

    // Add position size if provided
    if (positionSize) {
      message += `\n💼 Position Size: ${positionSize.toFixed(2)} lots`;
    }

    // Add reasoning
    if (signal.reasoning) {
      message += `\n\n💡 <b>Analysis:</b>\n${signal.reasoning}`;
    }

    return message;
  }

  /**
   * Format advanced signal message with full analysis
   */
  private formatAdvancedMessage(
    signal: AdvancedSignal,
    positionSize?: number
  ): string {
    let message = this.formatDetailedMessage(signal, positionSize);

    // Add advanced analysis
    if (signal.analysis) {
      message += `\n\n🔬 <b>Advanced Analysis:</b>\n`;

      // Price Action
      if (signal.analysis.priceAction) {
        message += `📊 Trend: ${
          signal.analysis.priceAction.trend
        } (${signal.analysis.priceAction.strength.toFixed(0)}%)\n`;
      }

      // ICT Analysis
      if (signal.analysis.ict) {
        const ictFactors = [];
        if (signal.analysis.ict.orderBlock) ictFactors.push('Order Block');
        if (signal.analysis.ict.fairValueGap) ictFactors.push('FVG');
        if (signal.analysis.ict.liquidityPool) ictFactors.push('Liquidity');
        if (ictFactors.length > 0) {
          message += `🎯 ICT: ${ictFactors.join(', ')}\n`;
        }
      }

      // SMC Analysis
      if (signal.analysis.smc) {
        const smcFactors = [];
        if (signal.analysis.smc.changeOfCharacter) smcFactors.push('CHoCH');
        if (signal.analysis.smc.breakOfStructure) smcFactors.push('BOS');
        if (smcFactors.length > 0) {
          message += `💎 SMC: ${smcFactors.join(', ')}\n`;
        }
        if (signal.analysis.smc.orderFlow !== 'neutral') {
          message += `🌊 Order Flow: ${signal.analysis.smc.orderFlow}\n`;
        }
      }

      // Wyckoff
      if (signal.analysis.wyckoff) {
        message += `📈 Wyckoff: ${signal.analysis.wyckoff.phase}\n`;
      }
    }

    return message;
  }

  /**
   * Send message to Telegram
   */
  public async sendMessage(message: TelegramMessage): Promise<void> {
    if (!this.isInitialized || !this.bot) {
      // Add to queue if not initialized
      this.messageQueue.push(message);
      return;
    }

    try {
      const options: any = {};
      if (message.parseMode) {
        options.parse_mode = message.parseMode;
      }

      await this.bot.sendMessage(message.chatId, message.text, options);
    } catch (error) {
      logger.telegram('error', 'Failed to send message', {
        error,
        message: message.text.substring(0, 100)
      });
      throw error;
    }
  }

  /**
   * Send market update
   */
  public async sendMarketUpdate(
    symbol: string,
    price: number,
    change: number
  ): Promise<void> {
    if (!this.telegramConfig.enabled) return;

    const emoji = change >= 0 ? '📈' : '📉';
    const changeText =
      change >= 0 ? `+${change.toFixed(5)}` : change.toFixed(5);

    const message =
      `${emoji} <b>${symbol}</b>\n` +
      `💰 Price: ${price.toFixed(5)}\n` +
      `📊 Change: ${changeText}\n` +
      `⏰ ${new Date().toLocaleString()}`;

    await this.sendMessage({
      chatId: this.telegramConfig.chatId,
      text: message,
      parseMode: 'HTML'
    });
  }

  /**
   * Command handlers
   */
  private async handleStartCommand(msg: any): Promise<void> {
    // Send typing indicator for immediate feedback
    await this.bot!.sendChatAction(msg.chat.id, 'typing');

    const welcomeMessage =
      `🤖 <b>Welcome to AI Trading Bot!</b>\n\n` +
      `This bot provides real-time forex trading signals based on advanced technical analysis.\n\n` +
      `<b>Features:</b>\n` +
      `📊 Technical Indicators (RSI, MACD, EMA, Bollinger Bands)\n` +
      `🕯️ Candlestick Pattern Recognition\n` +
      `🎯 ICT & SMC Analysis\n` +
      `📈 Price Action Analysis\n` +
      `🔬 Multi-timeframe Confluence\n\n` +
      `<b>Your Chat ID:</b> <code>${msg.chat.id}</code>\n\n` +
      `Use /help to see all available commands.`;

    // Send message directly for fastest response
    await this.bot!.sendMessage(msg.chat.id, welcomeMessage, {
      parse_mode: 'HTML'
    });
  }

  private async handleStatusCommand(msg: any): Promise<void> {
    // Immediate typing feedback
    await this.bot!.sendChatAction(msg.chat.id, 'typing');

    const statusMessage =
      `📊 <b>Bot Status</b>\n\n` +
      `✅ Status: Online\n` +
      `📈 Monitoring: ${config.trading.symbol}\n` +
      `🎯 Signals Today: 0\n` +
      `📊 Success Rate: N/A\n` +
      `⏰ Uptime: ${Math.floor(process.uptime() / 60)} minutes\n` +
      `🔄 Last Update: ${new Date().toLocaleString()}`;

    await this.bot!.sendMessage(msg.chat.id, statusMessage, {
      parse_mode: 'HTML'
    });
  }

  private async handleSignalsCommand(msg: any): Promise<void> {
    // Immediate typing feedback
    await this.bot!.sendChatAction(msg.chat.id, 'typing');

    const signalsMessage =
      `📋 <b>Recent Signals</b>\n\n` +
      `No recent signals available.\n` +
      `The bot will automatically send signals when market conditions are favorable.\n\n` +
      `Current monitoring: ${config.trading.symbol}`;

    await this.bot!.sendMessage(msg.chat.id, signalsMessage, {
      parse_mode: 'HTML'
    });
  }

  private async handleConfigCommand(msg: any): Promise<void> {
    // Immediate typing feedback
    await this.bot!.sendChatAction(msg.chat.id, 'typing');

    const configMessage =
      `⚙️ <b>Bot Configuration</b>\n\n` +
      `📊 Symbol: ${config.trading.symbol}\n` +
      `⏱️ Interval: ${config.trading.interval}\n` +
      `🎯 Min Confidence: ${config.trading.minConfidenceLevel}%\n` +
      `📈 Max Signals/Day: ${config.trading.maxSignalsPerDay}\n` +
      `💰 Risk/Reward: 1:${config.trading.riskRewardRatio}\n` +
      `🛑 Stop Loss: ${config.trading.stopLossPips} pips\n` +
      `🎯 Take Profit: ${config.trading.takeProfitPips} pips`;

    await this.bot!.sendMessage(msg.chat.id, configMessage, {
      parse_mode: 'HTML'
    });
  }

  private async handleHelpCommand(msg: any): Promise<void> {
    // Immediate typing feedback
    await this.bot!.sendChatAction(msg.chat.id, 'typing');

    const helpMessage =
      `🆘 <b>Available Commands</b>\n\n` +
      `/start - Welcome message and bot info\n` +
      `/status - Current bot status and statistics\n` +
      `/signals - View recent trading signals\n` +
      `/config - View current configuration\n` +
      `/help - Show this help message\n\n` +
      `<b>About Signals:</b>\n` +
      `The bot automatically analyzes market data and sends high-confidence trading signals. ` +
      `Each signal includes entry price, stop loss, take profit, and detailed analysis.\n\n` +
      `<b>Disclaimer:</b>\n` +
      `Trading involves risk. Always do your own research and never risk more than you can afford to lose.`;

    await this.bot!.sendMessage(msg.chat.id, helpMessage, {
      parse_mode: 'HTML'
    });
  }

  /**
   * Process message queue
   */
  private async processMessageQueue(): Promise<void> {
    if (this.isProcessingQueue || this.messageQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    try {
      while (this.messageQueue.length > 0) {
        const message = this.messageQueue.shift()!;
        await this.sendMessage(message);
        // Small delay to avoid rate limiting
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    } catch (error) {
      logger.telegram('error', 'Error processing message queue', { error });
    } finally {
      this.isProcessingQueue = false;
    }
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<TelegramConfig>): void {
    this.telegramConfig = { ...this.telegramConfig, ...newConfig };
    logger.telegram('info', 'Telegram configuration updated', {
      config: this.telegramConfig
    });
  }

  /**
   * Get current configuration
   */
  public getConfig(): TelegramConfig {
    return { ...this.telegramConfig };
  }

  /**
   * Check if service is initialized
   */
  public isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Stop the service
   */
  public async stop(): Promise<void> {
    if (this.bot) {
      await this.bot.stopPolling();
      this.bot = null;
    }
    this.isInitialized = false;
    logger.telegram('info', 'Telegram service stopped');
  }
}

// Export singleton instance
export const telegramService = new TelegramService();
