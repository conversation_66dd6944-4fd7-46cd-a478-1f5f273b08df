import cors from 'cors';
import express from 'express';
import helmet from 'helmet';
import morgan from 'morgan';
import { validateConfig } from './config';
import { logger } from './utils/logger';

// Initialize Express app for Vercel
const app = express();

// Setup middleware
app.use(helmet());

const url =
  process.env.NODE_ENV === 'production'
    ? process.env.APP_URL
    : 'http://localhost:3000';

app.use(
  cors({
    origin: url
  })
);

app.use(
  morgan('combined', {
    stream: {
      write: (message: string) => {
        logger.info('HTTP', message.trim());
      }
    }
  })
);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Setup routes
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0'
  });
});

app.get('/', (req, res) => {
  res.status(200).json({
    message: 'AI Trading Bot API',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      webhook: '/webhook'
    }
  });
});

// Webhook endpoint for TradingView signals
app.post('/webhook', async (req, res) => {
  try {
    logger.info('WEBHOOK', 'Received webhook request', {
      headers: req.headers,
      body: req.body
    });

    // Basic webhook validation
    if (!req.body) {
      return res.status(400).json({
        error: 'No data received',
        timestamp: new Date().toISOString()
      });
    }

    // Process the webhook (you can add your trading logic here)
    // For now, just log and respond
    logger.info('WEBHOOK', 'Processing trading signal', { signal: req.body });

    return res.status(200).json({
      status: 'success',
      message: 'Webhook received and processed',
      timestamp: new Date().toISOString(),
      received: req.body
    });
  } catch (error) {
    logger.error('WEBHOOK', 'Error processing webhook', { error });
    return res.status(500).json({
      error: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
});

// API routes
app.get('/api/status', (req, res) => {
  res.status(200).json({
    status: 'operational',
    services: {
      api: 'running',
      webhook: 'active'
    },
    timestamp: new Date().toISOString()
  });
});

// Catch-all route
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl,
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use(
  (
    error: any,
    _req: express.Request,
    res: express.Response,
    _next: express.NextFunction
  ) => {
    logger.error('APP', 'Unhandled error', {
      error: error.message,
      stack: error.stack
    });
    res.status(500).json({
      error: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
);

// Initialize configuration
try {
  validateConfig();
  logger.info('APP', 'Configuration validated for Vercel deployment');
} catch (error) {
  logger.error('APP', 'Configuration validation failed', { error });
}

// Export the Express app for Vercel
export default app;
