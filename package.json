{"name": "ai-trading-bot", "version": "1.0.0", "description": "Automated Forex Trading Bot with Exness Broker Integration, Technical Analysis, and Telegram Notifications", "main": "dist/index.js", "scripts": {"build": "npm run build", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "dev:watch": "nodemon --watch src --ext ts --exec ts-node src/index.ts", "dev:vercel": "vercel dev", "clean": "rm -rf dist", "prebuild": "npm run clean", "test": "echo \"Error: no test specified\" && exit 1", "lint": "echo \"<PERSON><PERSON> not configured yet\"", "type-check": "tsc --noEmit"}, "keywords": ["forex", "trading", "bot", "automated-trading", "exness", "technical-analysis", "telegram", "real-time", "websocket", "indicators", "broker-api"], "author": "Trading Bot Developer", "license": "MIT", "dependencies": {"@types/node": "^24.1.0", "@types/node-fetch": "^2.6.13", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.18.2", "helmet": "^8.1.0", "morgan": "^1.10.1", "node-fetch": "^3.3.2", "node-telegram-bot-api": "^0.66.0", "nodemon": "^3.1.10", "technicalindicators": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.9.2", "uuid": "^11.1.0", "ws": "^8.18.3"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/morgan": "^1.9.10", "@types/node-telegram-bot-api": "^0.64.9", "@types/ws": "^8.18.1"}}