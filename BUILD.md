# Build Guide for AI Trading Bot

## Overview
This document explains how to build and deploy the AI Trading Bot project.

## Prerequisites
- Node.js 18+ 
- npm or yarn
- TypeScript (installed as dependency)

## Build Scripts

### Development Build
```bash
# Basic build (TypeScript compilation)
npm run build

# Build with watch mode (rebuilds on file changes)
npm run build:watch
```

### Production Build
```bash
# Full production build with type checking and cleaning
npm run build:prod

# Vercel-specific build (used by Vercel deployment)
npm run vercel-build
```

### Utility Scripts
```bash
# Clean build artifacts
npm run clean

# Type check without building
npm run type-check

# Test the build by running it
npm run test:build
```

## Build Process

### 1. Development Build
```bash
npm run build
```
- Compiles TypeScript files from `src/` to `dist/`
- Generates source maps for debugging
- Includes type declarations

### 2. Production Build
```bash
npm run build:prod
```
- Cleans previous build artifacts
- Runs type checking
- Compiles TypeScript with optimizations
- Ready for deployment

### 3. Using Build Script
```bash
# Make executable (first time only)
chmod +x scripts/build.sh

# Run the build script
./scripts/build.sh
```

## Build Output

The build process creates the following structure:
```
dist/
├── config/
│   ├── index.js
│   ├── index.d.ts
│   └── index.js.map
├── services/
│   ├── indicators/
│   ├── patterns/
│   ├── signals/
│   ├── telegram/
│   └── tradingview/
├── types/
│   ├── index.js
│   ├── index.d.ts
│   └── index.js.map
├── utils/
│   ├── logger.js
│   ├── logger.d.ts
│   └── logger.js.map
├── index.js
├── index.d.ts
└── index.js.map
```

## Environment Configuration

1. Copy environment template:
```bash
cp .env.example .env
```

2. Configure your environment variables in `.env`

## Running the Built Application

### Development
```bash
npm start
```

### Production
```bash
npm run start:prod
```

## Deployment

### Vercel Deployment
```bash
# Deploy to preview
npm run deploy:preview

# Deploy to production
npm run deploy
```

### Manual Deployment
1. Build the project: `npm run build:prod`
2. Copy `dist/` folder to your server
3. Copy `package.json` and install production dependencies
4. Set environment variables
5. Start with `node dist/index.js`

## Troubleshooting

### Common Issues

1. **Build fails with TypeScript errors**
   - Run `npm run type-check` to see detailed errors
   - Fix TypeScript issues in source files

2. **Missing dependencies**
   - Run `npm install` to install all dependencies
   - Check `package.json` for correct versions

3. **Environment variables not loaded**
   - Ensure `.env` file exists and is properly configured
   - Check that `dotenv` is properly configured in your app

4. **Build output missing**
   - Check if `dist/` directory exists after build
   - Verify TypeScript configuration in `tsconfig.json`

### Build Verification
```bash
# Check if build was successful
ls -la dist/

# Test the built application
npm run test:build
```

## CI/CD Integration

The project includes GitHub Actions workflow for automated building and deployment:
- Located in `.github/workflows/deploy.yml`
- Triggers on push to `master` branch
- Runs type checking, builds, and deploys to Vercel

## Performance Tips

1. **Faster builds**: Use `npm run build:watch` during development
2. **Clean builds**: Run `npm run clean` before important builds
3. **Type checking**: Use `npm run type-check` to catch errors early
4. **Production optimization**: Always use `npm run build:prod` for deployment
