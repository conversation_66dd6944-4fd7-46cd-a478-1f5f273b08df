#!/bin/bash

# Build script for AI Trading Bot
set -e

echo "🚀 Starting build process..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "📦 Installing dependencies..."
npm ci

echo "🔍 Running type check..."
npm run type-check

echo "🧹 Cleaning previous build..."
npm run clean

echo "🔨 Building TypeScript..."
npm run build

echo "✅ Build completed successfully!"
echo "📁 Output directory: ./dist"
echo "🚀 To start the application: npm start"

# Check if dist directory exists and has files
if [ -d "dist" ] && [ "$(ls -A dist)" ]; then
    echo "📊 Build output:"
    ls -la dist/
else
    echo "❌ Build failed - no output files found"
    exit 1
fi
